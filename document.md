# Namecheap API Domain Search - Node.js Step by Step Guide

## Step 1: เตรียมความพร้อมและสร้างบัญชี

### 1.1 สร้างบัญชี Namecheap Sandbox
1. ไปที่ [Namecheap Sandbox](https://www.sandbox.namecheap.com/)
2. สร้างบัญชีใหม่สำหรับทดสอบ
3. เข้าสู่ระบบและไปที่ Profile → Tools → Business & Dev Tools → Namecheap API Access

### 1.2 เปิดใช้งาน API
1. คลิก "Enable API Access"
2. รอการอนุมัติ (อาจใช้เวลาสักครู่)
3. บันทึก API Key, API User, และ Username

### 1.3 ตรวจสอบ IP Address
```bash
# ตรวจสอบ IP address ของคุณ
curl ifconfig.me
```

## Step 2: ติดตั้ง Dependencies

### 2.1 สร้าง Project ใหม่
```bash
mkdir namecheap-domain-search
cd namecheap-domain-search
npm init -y
```

### 2.2 ติดตั้ง Required Packages
```bash
npm install axios xml2js dotenv
npm install --save-dev nodemon
```

### 2.3 อัพเดต package.json
```json
{
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js"
  }
}
```

## Step 3: สร้างไฟล์ Environment Variables

### 3.1 สร้างไฟล์ .env
```env
# Namecheap API Credentials
NAMECHEAP_API_USER=your-api-user
NAMECHEAP_API_KEY=your-api-key-here
NAMECHEAP_USERNAME=your-username
CLIENT_IP=your-ip-address

# Environment (sandbox or production)
NAMECHEAP_SANDBOX=true
```

### 3.2 สร้างไฟล์ .gitignore
```gitignore
node_modules/
.env
*.log
```

## Step 4: สร้าง Namecheap API Class

### 4.1 สร้างไฟล์ namecheap-api.js
```javascript
const axios = require('axios');
const xml2js = require('xml2js');
require('dotenv').config();

class NamecheapAPI {
    constructor() {
        this.apiUser = process.env.NAMECHEAP_API_USER;
        this.apiKey = process.env.NAMECHEAP_API_KEY;
        this.userName = process.env.NAMECHEAP_USERNAME;
        this.clientIp = process.env.CLIENT_IP;
        
        const isSandbox = process.env.NAMECHEAP_SANDBOX === 'true';
        this.baseUrl = isSandbox 
            ? 'https://api.sandbox.namecheap.com/xml.response'
            : 'https://api.namecheap.com/xml.response';
    }

    // ตรวจสอบการตั้งค่า credentials
    validateCredentials() {
        const required = [this.apiUser, this.apiKey, this.userName, this.clientIp];
        if (required.some(cred => !cred)) {
            throw new Error('Missing required credentials. Please check your .env file');
        }
    }

    // สร้าง base parameters สำหรับทุก API call
    getBaseParams(command) {
        return {
            ApiUser: this.apiUser,
            ApiKey: this.apiKey,
            UserName: this.userName,
            Command: command,
            ClientIp: this.clientIp
        };
    }

    // ส่ง API request
    async makeRequest(params) {
        try {
            const response = await axios.get(this.baseUrl, { params });
            return await this.parseXMLResponse(response.data);
        } catch (error) {
            throw new Error(`API request failed: ${error.message}`);
        }
    }

    // แปลง XML response เป็น JSON
    async parseXMLResponse(xmlString) {
        const parser = new xml2js.Parser({ explicitArray: false });
        try {
            const result = await parser.parseStringPromise(xmlString);
            
            // ตรวจสอบ API errors
            if (result.ApiResponse.Errors) {
                const error = result.ApiResponse.Errors.Error;
                throw new Error(`API Error: ${error._} (Code: ${error.$.Number})`);
            }

            return result.ApiResponse;
        } catch (error) {
            throw new Error(`XML parsing failed: ${error.message}`);
        }
    }
}

module.exports = NamecheapAPI;
```

## Step 5: สร้าง Domain Search Functions

### 5.1 สร้างไฟล์ domain-search.js
```javascript
const NamecheapAPI = require('./namecheap-api');

class DomainSearch extends NamecheapAPI {
    
    // ตรวจสอบความพร้อมใช้งานของโดเมน
    async checkDomains(domainList) {
        this.validateCredentials();
        
        const domains = Array.isArray(domainList) ? domainList.join(',') : domainList;
        
        const params = {
            ...this.getBaseParams('namecheap.domains.check'),
            DomainList: domains
        };

        try {
            const response = await this.makeRequest(params);
            return this.formatDomainCheckResults(response);
        } catch (error) {
            console.error('Domain check failed:', error.message);
            throw error;
        }
    }

    // จัดรูปแบบผลลัพธ์การตรวจสอบโดเมน
    formatDomainCheckResults(response) {
        const results = response.CommandResponse.DomainCheckResult;
        const domains = Array.isArray(results) ? results : [results];
        
        return domains.map(domain => ({
            domain: domain.$.Domain,
            available: domain.$.Available === 'true',
            isPremium: domain.$.IsPremiumName === 'true',
            premiumRegistrationPrice: domain.$.PremiumRegistrationPrice || null,
            premiumRenewalPrice: domain.$.PremiumRenewalPrice || null,
            premiumRestorePrice: domain.$.PremiumRestorePrice || null,
            icannFee: domain.$.IcannFee || null
        }));
    }

    // ดึงราคาโดเมน
    async getDomainPricing(productType = 'DOMAIN', actionName = 'REGISTER') {
        this.validateCredentials();
        
        const params = {
            ...this.getBaseParams('namecheap.users.getPricing'),
            ProductType: productType,
            ActionName: actionName
        };

        try {
            const response = await this.makeRequest(params);
            return this.formatPricingResults(response);
        } catch (error) {
            console.error('Pricing request failed:', error.message);
            throw error;
        }
    }

    // จัดรูปแบบผลลัพธ์ราคา
    formatPricingResults(response) {
        const productTypes = response.CommandResponse.UserGetPricingResult.ProductType;
        const types = Array.isArray(productTypes) ? productTypes : [productTypes];
        
        return types.map(type => ({
            productType: type.$.Name,
            prices: this.extractPrices(type.Price)
        }));
    }

    // ดึงราคาจาก Price array
    extractPrices(prices) {
        const priceArray = Array.isArray(prices) ? prices : [prices];
        return priceArray.map(price => ({
            duration: price.$.Duration,
            durationUnit: price.$.DurationUnit,
            price: parseFloat(price.$.Price),
            pricingType: price.$.PricingType,
            additionalCost: parseFloat(price.$.AdditionalCost || 0),
            regularPrice: parseFloat(price.$.RegularPrice || price.$.Price),
            regularPriceType: price.$.RegularPriceType,
            regularAdditionalCost: parseFloat(price.$.RegularAdditionalCost || 0),
            yourPrice: parseFloat(price.$.YourPrice || price.$.Price),
            yourPriceType: price.$.YourPriceType,
            yourAdditionalCost: parseFloat(price.$.YourAdditionalCost || 0),
            promotionPrice: parseFloat(price.$.PromotionPrice || 0),
            currency: price.$.Currency || 'USD'
        }));
    }

    // ค้นหาโดเมนที่แนะนำ (สำหรับโดเมนที่ไม่ว่าง)
    async getSuggestions(keyword, tldList = 'com,net,org,info,biz') {
        this.validateCredentials();
        
        const params = {
            ...this.getBaseParams('namecheap.domains.check'),
            DomainList: this.generateDomainSuggestions(keyword, tldList.split(','))
        };

        try {
            const response = await this.makeRequest(params);
            const results = this.formatDomainCheckResults(response);
            return results.filter(domain => domain.available);
        } catch (error) {
            console.error('Suggestion request failed:', error.message);
            throw error;
        }
    }

    // สร้างรายการโดเมนที่แนะนำ
    generateDomainSuggestions(keyword, tlds) {
        const suggestions = [];
        
        // เพิ่ม keyword + TLD
        tlds.forEach(tld => {
            suggestions.push(`${keyword}.${tld}`);
        });

        // เพิ่มคำนำหน้าและต่อท้าย
        const prefixes = ['get', 'my', 'the', 'best'];
        const suffixes = ['site', 'web', 'online', 'app'];

        prefixes.forEach(prefix => {
            tlds.forEach(tld => {
                suggestions.push(`${prefix}${keyword}.${tld}`);
            });
        });

        suffixes.forEach(suffix => {
            tlds.forEach(tld => {
                suggestions.push(`${keyword}${suffix}.${tld}`);
            });
        });

        return suggestions.slice(0, 20).join(','); // จำกัดไว้ที่ 20 รายการ
    }
}

module.exports = DomainSearch;
```

## Step 6: สร้าง Main Application

### 6.1 สร้างไฟล์ index.js
```javascript
const DomainSearch = require('./domain-search');
const readline = require('readline');

class DomainSearchApp {
    constructor() {
        this.domainSearch = new DomainSearch();
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    // แสดงเมนูหลัก
    showMenu() {
        console.log('\n=== Namecheap Domain Search ===');
        console.log('1. ตรวจสอบความพร้อมใช้งานโดเมน');
        console.log('2. ดูราคาโดเมน');
        console.log('3. หาโดเมนที่แนะนำ');
        console.log('4. ออกจากโปรแกรม');
        console.log('================================');
    }

    // รอรับ input จากผู้ใช้
    async askQuestion(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer);
            });
        });
    }

    // ตรวจสอบโดเมน
    async checkDomainsMenu() {
        try {
            const input = await this.askQuestion('กรอกชื่อโดเมนที่ต้องการตรวจสอบ (คั่นด้วยเครื่องหมายคอมมา): ');
            const domains = input.split(',').map(d => d.trim());
            
            console.log('\nกำลังตรวจสอบ...');
            const results = await this.domainSearch.checkDomains(domains);
            
            console.log('\n=== ผลการตรวจสอบ ===');
            results.forEach(domain => {
                const status = domain.available ? '✅ ว่าง' : '❌ ไม่ว่าง';
                console.log(`${domain.domain}: ${status}`);
                
                if (domain.isPremium && domain.available) {
                    console.log(`  💎 โดเมนพรีเมียม - ราคา: $${domain.premiumRegistrationPrice}`);
                }
            });
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาด:', error.message);
        }
    }

    // ดูราคาโดเมน
    async showPricingMenu() {
        try {
            console.log('\nกำลังดึงข้อมูลราคา...');
            const pricing = await this.domainSearch.getDomainPricing();
            
            console.log('\n=== ราคาโดเมน (1 ปี) ===');
            pricing.forEach(product => {
                console.log(`\n📋 ${product.productType.toUpperCase()}:`);
                
                const oneYearPrices = product.prices.filter(p => 
                    p.duration === '1' && p.durationUnit === 'YEAR'
                );
                
                oneYearPrices.forEach(price => {
                    console.log(`  💰 ราคา: $${price.yourPrice} ${price.currency}`);
                    if (price.promotionPrice > 0) {
                        console.log(`  🏷️  ราคาโปรโมชั่น: $${price.promotionPrice} ${price.currency}`);
                    }
                });
            });
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาด:', error.message);
        }
    }

    // หาโดเมนที่แนะนำ
    async showSuggestionsMenu() {
        try {
            const keyword = await this.askQuestion('กรอกคำค้นหา: ');
            const tlds = await this.askQuestion('กรอก TLD ที่ต้องการ (คั่นด้วยคอมมา, เช่น com,net,org): ') || 'com,net,org';
            
            console.log('\nกำลังค้นหาโดเมนที่แนะนำ...');
            const suggestions = await this.domainSearch.getSuggestions(keyword, tlds);
            
            console.log('\n=== โดเมนที่แนะนำ (ที่ยังว่าง) ===');
            if (suggestions.length === 0) {
                console.log('❌ ไม่พบโดเมนที่ว่าง');
            } else {
                suggestions.forEach(domain => {
                    console.log(`✅ ${domain.domain}`);
                    if (domain.isPremium) {
                        console.log(`  💎 โดเมนพรีเมียม - ราคา: $${domain.premiumRegistrationPrice}`);
                    }
                });
            }
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาด:', error.message);
        }
    }

    // เริ่มต้นแอปพลิเคชั่น
    async start() {
        console.log('🚀 เริ่มต้น Namecheap Domain Search Application');
        
        try {
            // ตรวจสอบการตั้งค่า
            this.domainSearch.validateCredentials();
            console.log('✅ การตั้งค่า API สำเร็จ');
        } catch (error) {
            console.error('❌ ข้อผิดพลาดในการตั้งค่า:', error.message);
            this.rl.close();
            return;
        }

        // เริ่ม main loop
        while (true) {
            this.showMenu();
            const choice = await this.askQuestion('เลือกเมนู (1-4): ');
            
            switch (choice) {
                case '1':
                    await this.checkDomainsMenu();
                    break;
                case '2':
                    await this.showPricingMenu();
                    break;
                case '3':
                    await this.showSuggestionsMenu();
                    break;
                case '4':
                    console.log('👋 ขอบคุณที่ใช้บริการ!');
                    this.rl.close();
                    return;
                default:
                    console.log('❌ กรุณาเลือกเมนู 1-4');
            }
        }
    }
}

// เริ่มต้นแอปพลิเคชั่น
if (require.main === module) {
    const app = new DomainSearchApp();
    app.start().catch(console.error);
}

module.exports = DomainSearchApp;
```

## Step 7: สร้าง Test File

### 7.1 สร้างไฟล์ test.js
```javascript
const DomainSearch = require('./domain-search');

async function runTests() {
    const domainSearch = new DomainSearch();
    
    console.log('🧪 เริ่มต้นการทดสอบ Namecheap API\n');

    try {
        // ทดสอบการตรวจสอบโดเมน
        console.log('1. ทดสอบการตรวจสอบโดเมน...');
        const domains = await domainSearch.checkDomains(['test123456789.com', 'google.com']);
        console.log('ผลลัพธ์:', domains);
        console.log('✅ ทดสอบการตรวจสอบโดเมนสำเร็จ\n');

        // ทดสอบการดึงราคา
        console.log('2. ทดสอบการดึงราคาโดเมน...');
        const pricing = await domainSearch.getDomainPricing();
        console.log('ราคาโดเมน .com:', pricing.find(p => p.productType === 'com'));
        console.log('✅ ทดสอบการดึงราคาสำเร็จ\n');

        // ทดสอบการหาโดเมนที่แนะนำ
        console.log('3. ทดสอบการหาโดเมนที่แนะนำ...');
        const suggestions = await domainSearch.getSuggestions('mytest', 'com,net');
        console.log('โดเมนที่แนะนำ:', suggestions.slice(0, 3));
        console.log('✅ ทดสอบการหาโดเมนที่แนะนำสำเร็จ\n');

        console.log('🎉 ทดสอบทั้งหมดสำเร็จ!');
        
    } catch (error) {
        console.error('❌ การทดสอบล้มเหลว:', error.message);
    }
}

runTests();
```

## Step 8: วิธีการรันโปรแกรม

### 8.1 ตั้งค่า Environment Variables
```bash
# คัดลอกไฟล์ .env.example และแก้ไข
cp .env.example .env
# แก้ไขไฟล์ .env ด้วย credentials ของคุณ
```

### 8.2 รันโปรแกรม
```bash
# รันในโหมดพัฒนา (auto-reload)
npm run dev

# หรือรันปกติ
npm start

# ทดสอบ API
node test.js
```

## Step 9: Advanced Features (เพิ่มเติม)

### 9.1 เพิ่ม Logging
```bash
npm install winston
```

### 9.2 เพิ่ม Rate Limiting
```bash
npm install bottleneck
```

### 9.3 เพิ่ม Caching
```bash
npm install node-cache
```

## Step 10: Troubleshooting

### 10.1 ข้อผิดพลาดที่พบบ่อย
- **API Key ไม่ถูกต้อง**: ตรวจสอบใน .env file
- **IP Address ไม่ตรงกัน**: อัพเดต CLIENT_IP ใน .env
- **Domain format ผิด**: ใส่ชื่อโดเมนเต็ม เช่น example.com
- **Rate limit exceeded**: รอสักครู่แล้วลองใหม่

### 10.2 Debug Mode
```javascript
// เพิ่มใน namecheap-api.js
async makeRequest(params) {
    if (process.env.DEBUG === 'true') {
        console.log('API Request:', params);
    }
    // ... rest of the code
}
```

## สรุป

คุณได้สร้าง Node.js application สำหรับค้นหาโดเมนผ่าน Namecheap API แล้ว! แอปพลิเคชั่นนี้สามารถ:

1. ✅ ตรวจสอบความพร้อมใช้งานของโดเมน
2. 💰 ดูราคาโดเมน
3. 💡 หาโดเมนที่แนะนำ
4. 🔍 รองรับโดเมนพรีเมียม
5. 🛡️ จัดการ error handling
6. 🎨 UI แบบ interactive console

ตอนนี้คุณสามารถพัฒนาต่อไปเป็น web API หรือเพิ่มฟีเจอร์อื่นๆ ได้ตามต้องการ!